package transaction

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/dto/response"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/transaction"
	"go.uber.org/zap"
)

// TransactionDataServiceInterface defines the interface for transaction data operations
type TransactionDataServiceInterface interface {
	GetTransactionData(ctx context.Context, userID uuid.UUID, dataType string, timeRange string) ([]*response.TransactionData, error)
}

// TransactionDataService implements transaction data operations
type TransactionDataService struct {
	affiliateRepo   transaction.AffiliateTransactionRepositoryInterface
	hyperLiquidRepo transaction.HyperLiquidTransactionRepositoryInterface
	commissionRepo  transaction.CommissionLedgerRepositoryInterface
	userRepo        transaction.UserRepositoryInterface
}

// NewTransactionDataService creates a new transaction data service
func NewTransactionDataService() TransactionDataServiceInterface {
	return &TransactionDataService{
		affiliateRepo:   transaction.NewAffiliateTransactionRepository(),
		hyperLiquidRepo: transaction.NewHyperLiquidTransactionRepository(),
		commissionRepo:  transaction.NewCommissionLedgerRepository(),
		userRepo:        transaction.NewUserRepository(),
	}
}

// GetTransactionData retrieves transaction data based on the specified data type and time range
func (s *TransactionDataService) GetTransactionData(ctx context.Context, userID uuid.UUID, dataType string, timeRange string) ([]*response.TransactionData, error) {
	// Get all direct referrals (level 1)
	// 如果没有找到推荐人，不应该return, 而是设置为0
	directReferrals, err := s.userRepo.GetDirectReferrals(ctx, userID)
	if err != nil {
		global.GVA_LOG.Warn("Failed to get direct referrals, setting to empty slice", zap.Error(err))
		directReferrals = []model.User{} // 设置为空切片而不是返回错误
	}

	// Get all downline users (up to 3 levels)
	// 如果没有找到下线用户，不应该return, 而是设置为空切片
	allDownlineUsers, err := s.userRepo.GetAllDownlineUsers(ctx, userID, 3)
	if err != nil {
		global.GVA_LOG.Warn("Failed to get downline users, setting to empty slice", zap.Error(err))
		allDownlineUsers = []uuid.UUID{} // 设置为空切片而不是返回错误
	}

	// Get transacting user count
	// 如果获取交易用户数量失败，不应该return, 而是设置为0
	transactingUserCount, err := s.getTransactingUserCount(ctx, userID)
	if err != nil {
		global.GVA_LOG.Warn("Failed to get transacting user count, setting to 0", zap.Error(err))
		transactingUserCount = 0 // 设置为0而不是返回错误
	}

	// Calculate time range
	startTime, endTime, err := s.calculateTimeRange(timeRange)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate time range: %w", err)
	}

	var transactionAmountUsd float64
	var claimedUsd float64
	var pendingClaimUsd float64

	// meme返佣查ActivityCashback，合约查CommissionLedger表

	switch dataType {
	case "ALL":
		// Calculate total MEME transaction amount from ActivityCashback table
		// 如果获取MEME交易金额失败，不应该return, 而是设置为0
		totalMemeAmount, err := s.getMemeTransactionAmountFromActivityCashback(ctx, allDownlineUsers, startTime, endTime)
		if err != nil {
			global.GVA_LOG.Warn("Failed to get total MEME amount from ActivityCashback, setting to 0", zap.Error(err))
			totalMemeAmount = decimal.Zero // 设置为0而不是返回错误
		}

		// Calculate total contract transaction amount from CommissionLedger table
		// 如果获取合约交易金额失败，不应该return, 而是设置为0
		totalContractAmount, err := s.getContractTransactionAmountFromCommissionLedger(ctx, allDownlineUsers, startTime, endTime)
		if err != nil {
			global.GVA_LOG.Warn("Failed to get total contract amount from CommissionLedger, setting to 0", zap.Error(err))
			totalContractAmount = decimal.Zero // 设置为0而不是返回错误
		}

		// Get claimed commission amount for the time period
		// 如果获取已领取佣金金额失败，不应该return, 而是设置为0
		claimedAmount, err := s.commissionRepo.GetClaimedAmountByUserIDAndTypeAndPeriod(ctx, userID, "ALL", startTime, endTime)
		if err != nil {
			global.GVA_LOG.Warn("Failed to get claimed amount, setting to 0", zap.Error(err))
			claimedAmount = decimal.Zero // 设置为0而不是返回错误
		}

		// Get pending claim amount for the time period
		// 如果获取待领取佣金金额失败，不应该return, 而是设置为0
		pendingClaimAmount, err := s.commissionRepo.GetPendingClaimAmountByUserIDAndTypeAndPeriod(ctx, userID, "ALL", startTime, endTime)
		if err != nil {
			global.GVA_LOG.Warn("Failed to get pending claim amount, setting to 0", zap.Error(err))
			pendingClaimAmount = decimal.Zero // 设置为0而不是返回错误
		}

		totalAmount := totalMemeAmount.Add(totalContractAmount)
		transactionAmountUsd, _ = totalAmount.Float64()
		claimedUsd, _ = claimedAmount.Float64()
		pendingClaimUsd, _ = pendingClaimAmount.Float64()

	case "MEME":
		// Calculate total MEME transaction amount from ActivityCashback table
		// 如果获取MEME交易金额失败，不应该return, 而是设置为0
		totalMemeAmount, err := s.getMemeTransactionAmountFromActivityCashback(ctx, allDownlineUsers, startTime, endTime)
		if err != nil {
			global.GVA_LOG.Warn("Failed to get total MEME amount from ActivityCashback, setting to 0", zap.Error(err))
			totalMemeAmount = decimal.Zero // 设置为0而不是返回错误
		}

		// Get claimed MEME commission amount for the time period
		// 如果获取已领取MEME佣金金额失败，不应该return, 而是设置为0
		claimedMemeAmount, err := s.commissionRepo.GetClaimedAmountByUserIDAndTypeAndPeriod(ctx, userID, "MEME", startTime, endTime)
		if err != nil {
			global.GVA_LOG.Warn("Failed to get claimed MEME amount, setting to 0", zap.Error(err))
			claimedMemeAmount = decimal.Zero // 设置为0而不是返回错误
		}

		// Get pending claim MEME amount for the time period
		// 如果获取待领取MEME佣金金额失败，不应该return, 而是设置为0
		pendingClaimMemeAmount, err := s.commissionRepo.GetPendingClaimAmountByUserIDAndTypeAndPeriod(ctx, userID, "MEME", startTime, endTime)
		if err != nil {
			global.GVA_LOG.Warn("Failed to get pending claim MEME amount, setting to 0", zap.Error(err))
			pendingClaimMemeAmount = decimal.Zero // 设置为0而不是返回错误
		}

		transactionAmountUsd, _ = totalMemeAmount.Float64()
		claimedUsd, _ = claimedMemeAmount.Float64()
		pendingClaimUsd, _ = pendingClaimMemeAmount.Float64()

	case "CONTRACT":
		// Calculate total contract transaction amount from CommissionLedger table
		// 如果获取合约交易金额失败，不应该return, 而是设置为0
		totalContractAmount, err := s.getContractTransactionAmountFromCommissionLedger(ctx, allDownlineUsers, startTime, endTime)
		if err != nil {
			global.GVA_LOG.Warn("Failed to get total contract amount from CommissionLedger, setting to 0", zap.Error(err))
			totalContractAmount = decimal.Zero // 设置为0而不是返回错误
		}

		// Get claimed contract commission amount for the time period
		// 如果获取已领取合约佣金金额失败，不应该return, 而是设置为0
		claimedContractAmount, err := s.commissionRepo.GetClaimedAmountByUserIDAndTypeAndPeriod(ctx, userID, "CONTRACT", startTime, endTime)
		if err != nil {
			global.GVA_LOG.Warn("Failed to get claimed contract amount, setting to 0", zap.Error(err))
			claimedContractAmount = decimal.Zero // 设置为0而不是返回错误
		}

		// Get pending claim contract amount for the time period
		// 如果获取待领取合约佣金金额失败，不应该return, 而是设置为0
		pendingClaimContractAmount, err := s.commissionRepo.GetPendingClaimAmountByUserIDAndTypeAndPeriod(ctx, userID, "CONTRACT", startTime, endTime)
		if err != nil {
			global.GVA_LOG.Warn("Failed to get pending claim contract amount, setting to 0", zap.Error(err))
			pendingClaimContractAmount = decimal.Zero // 设置为0而不是返回错误
		}

		transactionAmountUsd, _ = totalContractAmount.Float64()
		claimedUsd, _ = claimedContractAmount.Float64()
		pendingClaimUsd, _ = pendingClaimContractAmount.Float64()

	default:
		return nil, fmt.Errorf("unsupported data type: %s", dataType)
	}

	// Get invitation count for the time period
	invitationCount, err := s.userRepo.GetInvitationCountByUserIDAndPeriod(ctx, userID, startTime, endTime)
	if err != nil {
		global.GVA_LOG.Error("Failed to get invitation count", zap.Error(err))
		// Use fallback to total direct referrals if period-specific count fails
		invitationCount = len(directReferrals)
	}

	// 合约交易量查询ReferralSnapshot的TotalPerpsVolumeUSD
	// 新增合约交易量返回 ReferralSnapshot的TotalMemeVolumeUSD
	// Get contract and MEME volume from ReferralSnapshot
	contractVolumeUsd, memeVolumeUsd, err := s.getVolumeFromReferralSnapshot(ctx, userID)
	if err != nil {
		global.GVA_LOG.Warn("Failed to get volume from ReferralSnapshot, setting to 0", zap.Error(err))
		contractVolumeUsd = 0
		memeVolumeUsd = 0
	}

	// Create transaction data
	result := &response.TransactionData{
		ClaimedUsd:           claimedUsd,
		PendingClaimUsd:      pendingClaimUsd,
		ContractVolumeUsd:    contractVolumeUsd,
		MemeVolumeUsd:        memeVolumeUsd,
		InvitationCount:      invitationCount,
		TransactingUserCount: transactingUserCount,
		TransactionAmountUsd: transactionAmountUsd,
	}

	return []*response.TransactionData{result}, nil
}

// calculateTimeRange calculates the start and end time based on the time range string
func (s *TransactionDataService) calculateTimeRange(timeRange string) (time.Time, time.Time, error) {
	now := time.Now().UTC()
	var startTime time.Time

	switch timeRange {
	case "TODAY":
		startTime = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.UTC)
	case "LAST_30_DAYS":
		startTime = now.AddDate(0, 0, -30)
	case "LAST_60_DAYS":
		startTime = now.AddDate(0, 0, -60)
	case "ALL_TIME":
		startTime = time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC) // 设置一个足够早的开始时间
	default:
		return time.Time{}, time.Time{}, fmt.Errorf("unsupported time range: %s", timeRange)
	}

	return startTime, now, nil
}

// getTransactingUserCount calculates the number of users who have made transactions
func (s *TransactionDataService) getTransactingUserCount(ctx context.Context, userID uuid.UUID) (int, error) {
	// 获取三级用户中 FirstTransactionAt 不为 null 的用户数量
	// 通过 Referral 表关联 User 表，统计三级用户中有交易记录的用户数量

	var count int64
	err := global.GVA_DB.WithContext(ctx).
		Model(&model.User{}).
		Joins("JOIN referrals ON users.id = referrals.user_id").
		Where("referrals.referrer_id = ? AND referrals.depth <= 3", userID).
		Where("users.first_transaction_at IS NOT NULL").
		Count(&count).Error

	if err != nil {
		// 如果查询失败，返回0而不是错误
		global.GVA_LOG.Warn("Failed to get transacting user count",
			zap.String("user_id", userID.String()),
			zap.Error(err))
		return 0, nil
	}

	return int(count), nil
}

// getMemeTransactionAmountFromActivityCashback calculates the total MEME transaction amount from ActivityCashback table
func (s *TransactionDataService) getMemeTransactionAmountFromActivityCashback(ctx context.Context, userIDs []uuid.UUID, startTime, endTime time.Time) (decimal.Decimal, error) {
	if len(userIDs) == 0 {
		return decimal.Zero, nil
	}

	var result struct {
		TotalAmount decimal.Decimal `json:"total_amount"`
	}

	// Convert times to UTC and format for database query
	startTimeUTC := startTime.UTC()
	endTimeUTC := endTime.UTC()

	err := global.GVA_DB.WithContext(ctx).
		Model(&model.ActivityCashback{}).
		Select("COALESCE(SUM(cashback_amount_usd), 0) as total_amount").
		Where("user_id IN ?", userIDs).
		Where("created_at::timestamp >= ?::timestamp AND created_at::timestamp < ?::timestamp", startTimeUTC, endTimeUTC).
		Scan(&result).Error

	if err != nil {
		global.GVA_LOG.Warn("Failed to get total MEME amount from ActivityCashback, returning 0", zap.Error(err))
		return decimal.Zero, nil
	}

	return result.TotalAmount, nil
}

// getContractTransactionAmountFromCommissionLedger calculates the total contract transaction amount from CommissionLedger table
func (s *TransactionDataService) getContractTransactionAmountFromCommissionLedger(ctx context.Context, userIDs []uuid.UUID, startTime, endTime time.Time) (decimal.Decimal, error) {
	if len(userIDs) == 0 {
		return decimal.Zero, nil
	}

	var result struct {
		TotalAmount decimal.Decimal `json:"total_amount"`
	}

	// Convert times to UTC and format for database query
	startTimeUTC := startTime.UTC()
	endTimeUTC := endTime.UTC()

	err := global.GVA_DB.WithContext(ctx).
		Model(&model.CommissionLedger{}).
		Select("COALESCE(SUM(commission_amount), 0) as total_amount").
		Where("source_user_id IN ?", userIDs).
		Where("source_transaction_type = ?", "CONTRACT").
		Where("created_at::timestamp >= ?::timestamp AND created_at::timestamp < ?::timestamp", startTimeUTC, endTimeUTC).
		Scan(&result).Error

	if err != nil {
		global.GVA_LOG.Warn("Failed to get total contract amount from CommissionLedger, returning 0", zap.Error(err))
		return decimal.Zero, nil
	}

	return result.TotalAmount, nil
}

// getVolumeFromReferralSnapshot retrieves contract and MEME volume from ReferralSnapshot table
func (s *TransactionDataService) getVolumeFromReferralSnapshot(ctx context.Context, userID uuid.UUID) (float64, float64, error) {
	var snapshot model.ReferralSnapshot
	err := global.GVA_DB.WithContext(ctx).
		Model(&model.ReferralSnapshot{}).
		Select("total_perps_volume_usd, total_meme_volume_usd").
		Where("user_id = ?", userID).
		First(&snapshot).Error

	if err != nil {
		// 如果查询失败，返回0而不是错误，因为可能用户还没有snapshot记录
		global.GVA_LOG.Warn("Failed to get referral snapshot for user",
			zap.String("user_id", userID.String()),
			zap.Error(err))
		return 0, 0, nil
	}

	contractVolumeUsd, _ := snapshot.TotalPerpsVolumeUSD.Float64()
	memeVolumeUsd, _ := snapshot.TotalMemeVolumeUSD.Float64()

	return contractVolumeUsd, memeVolumeUsd, nil
}
