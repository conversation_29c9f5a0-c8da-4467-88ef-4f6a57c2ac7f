package response

// TransactionData represents unified transaction data response
type TransactionData struct {
	// 已领取返佣金额(USD)
	ClaimedUsd float64 `json:"claimedUsd"`
	// 待领取返佣金额(USD)
	PendingClaimUsd float64 `json:"pendingClaimUsd"`
	// 衍生品交易量(USD) - 合约交易量
	ContractVolumeUsd float64 `json:"contractVolumeUsd"`
	// Meme交易量(USD)
	MemeVolumeUsd float64 `json:"memeVolumeUsd"`
	// 受邀人数
	InvitationCount int `json:"invitationCount"`
	// 交易人数
	TransactingUserCount int `json:"transactingUserCount"`
	// 总交易金额(USD) - 用于内部计算，保持向后兼容
	TransactionAmountUsd float64 `json:"transactionAmountUsd"`
}

// TransactionDataResponse represents the response for transaction data query
type TransactionDataResponse struct {
	TransactionData []*TransactionData `json:"transactionData"`
	Success         bool               `json:"success"`
	Message         string             `json:"message,omitempty"`
}
