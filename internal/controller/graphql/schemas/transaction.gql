# Transaction data schema

type TransactionData {
  # 已领取返佣金额(USD)
  claimedUsd: Float!
  # 待领取返佣金额(USD)
  pendingClaimUsd: Float!
  # 衍生品交易量(USD) - 合约交易量
  contractVolumeUsd: Float!
  # Meme交易量(USD)
  memeVolumeUsd: Float!
  # 受邀人数
  invitationCount: Int!
  # 交易人数
  transactingUserCount: Int!
  # 总交易金额(USD) - 用于内部计算，保持向后兼容
  transactionAmountUsd: Float!
}

enum TransactionDataType {
  ALL
  MEME
  CONTRACT
}

enum TimeRangeType {
  TODAY
  LAST_30_DAYS
  LAST_60_DAYS
  ALL_TIME
}

input TransactionDataInput {
  dataType: TransactionDataType!
  timeRange: TimeRangeType!
}

type TransactionDataResponse {
  transactionData: [TransactionData!]!
  success: Boolean!
  message: String
}
