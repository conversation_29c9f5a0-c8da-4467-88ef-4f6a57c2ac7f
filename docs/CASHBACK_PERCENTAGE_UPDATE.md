# Cashback Percentage API Update

## Overview

This document describes the changes made to update the `cashbackPercentage` field in all APIs to return percentage values (multiplied by 100) instead of decimal values for better frontend display.

## Problem

Previously, the `cashbackPercentage` field was returned as decimal values (e.g., 0.0020 for 0.2% cashback), which required frontend to multiply by 100 for proper percentage display. This was inconsistent and confusing.

## Solution

Updated all API responses to return `cashbackPercentage` already multiplied by 100, so:
- Database stores: `0.0020` (decimal)
- API returns: `0.20` (percentage ready for display)
- Frontend displays: `0.20%`

## Changes Made

### 1. User GraphQL API (`internal/controller/graphql/resolvers/activity_cashback.go`)

**Function: `convertTierBenefitToGQL`**
```go
// Before
CashbackPercentage: cashbackPercentage,
NetFee:             netFee,

// After  
CashbackPercentage: cashbackPercentage * 100, // Convert to percentage (multiply by 100)
NetFee:             netFee * 100,             // Convert to percentage (multiply by 100)
```

### 2. Admin GraphQL API (`internal/controller/admin/graphql/resolvers/admin_activity_cashback.go`)

**Function: `convertTierBenefitToGQL`**
```go
// Before
CashbackPercentage: cashbackPercentage,
NetFee:             netFee,

// After
CashbackPercentage: cashbackPercentage * 100, // Convert to percentage (multiply by 100)
NetFee:             netFee * 100,             // Convert to percentage (multiply by 100)
```

**Function: `convertTierBenefitToAdminGQL`**
```go
// Before
CashbackPercentage: cashbackPercentage,
NetFee:             netFee,

// After
CashbackPercentage: cashbackPercentage * 100, // Convert to percentage (multiply by 100)
NetFee:             netFee * 100,             // Convert to percentage (multiply by 100)
```

**Admin Create Tier Benefit**
```go
// Before
CashbackPercentage: decimal.NewFromFloat(input.CashbackPercentage),
NetFee:             decimal.NewFromFloat(input.NetFee),

// After
CashbackPercentage: decimal.NewFromFloat(input.CashbackPercentage / 100), // Convert from percentage to decimal (divide by 100)
NetFee:             decimal.NewFromFloat(input.NetFee / 100),             // Convert from percentage to decimal (divide by 100)
```

**Admin Update Tier Benefit**
```go
// Before
existingTierBenefit.CashbackPercentage = decimal.NewFromFloat(*input.CashbackPercentage)
existingTierBenefit.NetFee = decimal.NewFromFloat(*input.NetFee)

// After
existingTierBenefit.CashbackPercentage = decimal.NewFromFloat(*input.CashbackPercentage / 100) // Convert from percentage to decimal (divide by 100)
existingTierBenefit.NetFee = decimal.NewFromFloat(*input.NetFee / 100) // Convert from percentage to decimal (divide by 100)
```

## API Response Examples

### Before Changes
```json
{
  "tierBenefit": {
    "tierLevel": 2,
    "tierName": "Silver", 
    "cashbackPercentage": 0.0020,
    "netFee": 0.0000
  }
}
```

### After Changes
```json
{
  "tierBenefit": {
    "tierLevel": 2,
    "tierName": "Silver",
    "cashbackPercentage": 0.20,
    "netFee": 0.00
  }
}
```

## Database Schema

**No changes to database schema.** Values are still stored as decimals:
- `0.0010` = 0.1% cashback
- `0.0015` = 0.15% cashback  
- `0.0020` = 0.2% cashback
- `0.0025` = 0.25% cashback
- `0.0030` = 0.3% cashback

## Affected APIs

### User APIs
- `activityCashbackDashboard` - Returns current and next tier cashback percentages
- `tierBenefits` - Returns all tier benefits with cashback percentages
- `activityCashbackSummary` - Returns tier benefit information

### Admin APIs  
- `adminGetTierBenefits` - Returns all tier benefits for admin management
- `createTierBenefit` - Creates new tier benefit (input now expects percentage values)
- `updateTierBenefit` - Updates tier benefit (input now expects percentage values)

## Testing

### Unit Tests Added
- `internal/controller/graphql/resolvers/cashback_percentage_test.go`
- `internal/controller/admin/graphql/resolvers/cashback_percentage_test.go`

### Test Script
- `scripts/test-cashback-percentage.sh` - Manual testing script for API endpoints

### Running Tests
```bash
# Test user API conversion
go test -v ./internal/controller/graphql/resolvers -run TestCashbackPercentageConversion

# Test admin API conversion  
go test -v ./internal/controller/admin/graphql/resolvers -run TestAdminInputConversion
```

## Migration Notes

### For Frontend Developers
- **No changes needed** - APIs now return percentage-ready values
- Remove any `* 100` multiplication in frontend code
- Values like `0.20` should be displayed as `0.20%`

### For Admin Interface
- Admin input should now accept percentage values (e.g., `0.20` for 0.2% cashback)
- Backend will automatically convert to decimal for database storage

## Backward Compatibility

This is a **breaking change** for API consumers. Frontend applications will need to:
1. Remove percentage conversion logic (`value * 100`)
2. Update display formatting to handle the new percentage values
3. Update admin forms to input percentage values instead of decimal values

## Verification

To verify the changes work correctly:

1. **Check API Response**: Values should be > 1 for typical cashback rates
   - `0.10` instead of `0.0010` 
   - `0.20` instead of `0.0020`

2. **Check Database**: Values should remain as decimals
   - `0.0010`, `0.0020`, etc.

3. **Check Admin Input**: Should accept percentage values
   - Input `0.25` should store `0.0025` in database
   - Should return `0.25` in API response
