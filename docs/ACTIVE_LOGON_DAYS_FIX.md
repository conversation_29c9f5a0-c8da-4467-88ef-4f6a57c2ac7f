# Active Logon Days Calculation Fix

## Problem Description

The `activeLogonDays` field in the Activity Cashback system was not being reset properly at the beginning of each month, causing the count to accumulate indefinitely across months.

### Root Cause Analysis

1. **Missing Monthly Reset Check**: The `GetUserTierInfo` function did not check if monthly stats needed to be reset before returning user data.

2. **Inconsistent Reset Logic**: Monthly reset was only performed by background jobs, but not during regular API calls when users accessed their data.

3. **Race Condition**: If a user logged in before the monthly background job ran (scheduled for 1st of month at 01:00 UTC), their `activeLogonDays` would continue accumulating from the previous month.

## Solution Implemented

### 1. Enhanced `GetUserTierInfo` Function

Modified the `GetUserTierInfo` function in `TierManagementService` to automatically check and perform monthly reset when needed:

```go
// GetUserTierInfo retrieves user tier information
func (s *TierManagementService) GetUserTierInfo(ctx context.Context, userID uuid.UUID) (*model.UserTierInfo, error) {
    tierInfo, err := s.tierInfoRepo.GetByUserID(ctx, userID)
    if err != nil {
        if err == gorm.ErrRecordNotFound {
            return s.CreateUserTierInfo(ctx, userID)
        }
        return nil, fmt.Errorf("failed to get user tier info: %w", err)
    }

    // Check if monthly reset is needed before returning the data
    if tierInfo.ShouldResetMonthly() {
        tierInfo.ResetMonthlyStats()
        if err := s.UpdateUserTierInfo(ctx, tierInfo); err != nil {
            // Log error but continue with reset data
            global.GVA_LOG.Error("Failed to reset monthly stats for user", zap.Error(err))
        }
    }

    return tierInfo, nil
}
```

### 2. Enhanced Logging in `UpdateActivity`

Added debug logging to track activity updates for better monitoring:

```go
// Log before and after activity updates
global.GVA_LOG.Debug("Updating user activity", 
    zap.String("user_id", userID.String()),
    zap.Int("current_active_days", tierInfo.ActiveDaysThisMonth))
```

### 3. Existing Logic Verification

Verified that the existing model-level logic is correct:

- `ShouldResetMonthly()`: Properly checks if current month/year differs from `MonthlyResetAt`
- `ResetMonthlyStats()`: Correctly resets `ActiveDaysThisMonth` and `PointsThisMonth` to 0
- `UpdateActivity()`: Properly increments active days only for new days

## Impact of the Fix

### Before Fix
- `activeLogonDays` would accumulate across months
- Users could have values like 45, 60, 100+ active days in a month
- Monthly reset only happened via background job at specific time

### After Fix
- `activeLogonDays` is automatically reset when accessing user data in a new month
- Maximum possible value is 31 (days in a month)
- Reset happens immediately when user accesses their data, not just via background job

## Testing

### 1. Unit Tests
Created comprehensive unit tests in `tier_management_service_test.go`:
- Test monthly reset when needed
- Test no reset when not needed  
- Test reset when `MonthlyResetAt` is nil
- Test activity update logic

### 2. Integration Testing
Use the provided scripts:
- `scripts/test-active-logon-days-fix.sh`: API-level testing
- `scripts/debug-active-logon-days.sql`: Database-level debugging

### 3. Manual Testing Steps

1. **Check current state**:
   ```sql
   SELECT user_id, active_days_this_month, monthly_reset_at 
   FROM user_tier_info WHERE user_id = 'YOUR_USER_ID';
   ```

2. **Simulate previous month** (for testing):
   ```sql
   UPDATE user_tier_info 
   SET monthly_reset_at = DATE_TRUNC('month', NOW() - INTERVAL '1 month') 
   WHERE user_id = 'YOUR_USER_ID';
   ```

3. **Call API and verify reset**:
   ```graphql
   query { 
     activityCashbackSummary { 
       data { activeLogonDays } 
     } 
   }
   ```

4. **Verify activeLogonDays is 0 after API call**

## Monitoring and Alerts

### Recommended Monitoring
1. **High Active Days Alert**: Alert if any user has `activeLogonDays > 31`
2. **Reset Failure Alert**: Monitor logs for "Failed to reset monthly stats" errors
3. **Monthly Reset Job**: Ensure background job continues to run successfully

### Database Queries for Monitoring
```sql
-- Find users with suspicious high active days
SELECT user_id, active_days_this_month 
FROM user_tier_info 
WHERE active_days_this_month > 31;

-- Check monthly reset status
SELECT 
    COUNT(*) as total_users,
    COUNT(CASE WHEN monthly_reset_at IS NULL THEN 1 END) as null_reset,
    COUNT(CASE WHEN monthly_reset_at < DATE_TRUNC('month', NOW()) THEN 1 END) as needs_reset
FROM user_tier_info;
```

## Backward Compatibility

This fix is backward compatible:
- No API changes
- No database schema changes
- Existing data is automatically corrected on next access
- Background jobs continue to work as before

## Performance Considerations

- Minimal performance impact: Reset check is a simple date comparison
- Database update only happens when reset is needed (once per user per month)
- No additional database queries for normal operations

## Future Improvements

1. **Batch Reset Optimization**: Consider batching resets for better performance
2. **Caching**: Cache reset status to avoid repeated checks
3. **Metrics**: Add metrics for reset operations and active days distribution
