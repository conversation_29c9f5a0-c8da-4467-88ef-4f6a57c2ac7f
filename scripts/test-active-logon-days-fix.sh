#!/bin/bash

# Test script to verify activeLogonDays calculation fix
# This script tests the monthly reset logic for active logon days

set -e

# Configuration
BASE_URL="http://localhost:8080"
GRAPHQL_ENDPOINT="$BASE_URL/graphql"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to make GraphQL request
make_graphql_request() {
    local query=$1
    local token=$2
    
    curl -s -X POST "$GRAPHQL_ENDPOINT" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $token" \
        -d "{\"query\": \"$query\"}"
}

# Function to get activity cashback summary
get_activity_summary() {
    local token=$1
    local query='query { activityCashbackSummary { success message data { currentLevel currentLevelName activeLogonDays accumulatedTradingVolumeUsd currentScore } } }'
    
    make_graphql_request "$query" "$token"
}

# Function to simulate user login (daily checkin)
simulate_login() {
    local token=$1
    local query='mutation { completeTask(taskId: "daily_checkin") { success message } }'
    
    make_graphql_request "$query" "$token"
}

# Main test function
run_test() {
    local test_name=$1
    local token=$2
    
    print_status $BLUE "=== Testing: $test_name ==="
    
    # Get initial state
    print_status $YELLOW "Getting initial activity summary..."
    local initial_response=$(get_activity_summary "$token")
    local initial_active_days=$(echo "$initial_response" | jq -r '.data.activityCashbackSummary.data.activeLogonDays // 0')
    
    print_status $GREEN "Initial active days: $initial_active_days"
    
    # Simulate login
    print_status $YELLOW "Simulating user login..."
    local login_response=$(simulate_login "$token")
    local login_success=$(echo "$login_response" | jq -r '.data.completeTask.success // false')
    
    if [ "$login_success" = "true" ]; then
        print_status $GREEN "Login simulation successful"
    else
        print_status $RED "Login simulation failed"
        echo "Response: $login_response"
    fi
    
    # Get updated state
    print_status $YELLOW "Getting updated activity summary..."
    local updated_response=$(get_activity_summary "$token")
    local updated_active_days=$(echo "$updated_response" | jq -r '.data.activityCashbackSummary.data.activeLogonDays // 0')
    
    print_status $GREEN "Updated active days: $updated_active_days"
    
    # Check if active days increased correctly
    if [ "$updated_active_days" -gt "$initial_active_days" ]; then
        print_status $GREEN "✓ Active days increased correctly"
    elif [ "$updated_active_days" -eq "$initial_active_days" ]; then
        print_status $YELLOW "⚠ Active days unchanged (user may have already logged in today)"
    else
        print_status $RED "✗ Active days decreased unexpectedly"
    fi
    
    # Display full summary
    print_status $BLUE "Full Activity Summary:"
    echo "$updated_response" | jq '.data.activityCashbackSummary.data' 2>/dev/null || echo "$updated_response"
    
    echo ""
}

# Function to test monthly reset logic
test_monthly_reset() {
    print_status $BLUE "=== Testing Monthly Reset Logic ==="
    
    # This would require admin access or database manipulation
    # For now, we'll just document what should be tested
    
    print_status $YELLOW "Manual test steps for monthly reset:"
    echo "1. Check current activeLogonDays for a user"
    echo "2. Manually update monthly_reset_at to previous month in database"
    echo "3. Call activityCashbackSummary API"
    echo "4. Verify activeLogonDays is reset to 0"
    echo "5. Simulate login and verify activeLogonDays increases to 1"
    
    print_status $BLUE "SQL commands for manual testing:"
    echo "-- Check current state"
    echo "SELECT user_id, active_days_this_month, monthly_reset_at FROM user_tier_info WHERE user_id = 'YOUR_USER_ID';"
    echo ""
    echo "-- Simulate previous month reset"
    echo "UPDATE user_tier_info SET monthly_reset_at = DATE_TRUNC('month', NOW() - INTERVAL '1 month') WHERE user_id = 'YOUR_USER_ID';"
    echo ""
    echo "-- Reset to current month (after testing)"
    echo "UPDATE user_tier_info SET monthly_reset_at = NOW() WHERE user_id = 'YOUR_USER_ID';"
}

# Main execution
main() {
    print_status $GREEN "Active Logon Days Fix Test Script"
    print_status $GREEN "================================="
    
    # Check if jq is installed
    if ! command -v jq &> /dev/null; then
        print_status $RED "Error: jq is required but not installed"
        exit 1
    fi
    
    # Check if token is provided
    if [ -z "$1" ]; then
        print_status $RED "Usage: $0 <JWT_TOKEN>"
        print_status $YELLOW "Example: $0 eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
        exit 1
    fi
    
    local token=$1
    
    # Test basic functionality
    run_test "Basic Active Days Tracking" "$token"
    
    # Test monthly reset logic
    test_monthly_reset
    
    print_status $GREEN "Test completed!"
    print_status $YELLOW "Note: For complete testing, manually verify monthly reset logic using the SQL commands above."
}

# Run main function with all arguments
main "$@"
