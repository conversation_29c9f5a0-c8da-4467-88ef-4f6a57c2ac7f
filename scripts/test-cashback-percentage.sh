#!/bin/bash

# Test script to verify cashbackPercentage is returned as percentage (multiplied by 100)
# This script tests both user and admin APIs

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
BASE_URL="http://localhost:8080"
GRAPHQL_ENDPOINT="$BASE_URL/graphql"
ADMIN_GRAPHQL_ENDPOINT="$BASE_URL/admin/graphql"

# Test JWT token (you may need to update this)
TEST_JWT="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.test"

echo -e "${BLUE}🧪 Testing Cashback Percentage API Changes${NC}"
echo "========================================"

# Function to make GraphQL request
make_graphql_request() {
    local endpoint=$1
    local query=$2
    local token=$3
    
    curl -s -X POST "$endpoint" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $token" \
        -d "{\"query\": \"$query\"}"
}

# Test 1: Get tier benefits (user API)
echo -e "\n${BLUE}Test 1: User API - Get Tier Benefits${NC}"
echo "------------------------------------"

USER_TIER_QUERY='query {
  tierBenefits {
    success
    message
    data {
      id
      tierLevel
      tierName
      minPoints
      cashbackPercentage
      netFee
      isActive
    }
  }
}'

echo "Making request to user API..."
USER_RESPONSE=$(make_graphql_request "$GRAPHQL_ENDPOINT" "$USER_TIER_QUERY" "$TEST_JWT")

if echo "$USER_RESPONSE" | jq -e '.data.tierBenefits.success' > /dev/null 2>&1; then
    echo -e "${GREEN}✅ User API request successful${NC}"
    
    # Extract and display cashback percentages
    echo -e "\n${BLUE}Cashback Percentages (should be displayed as percentages):${NC}"
    echo "$USER_RESPONSE" | jq -r '.data.tierBenefits.data[]? | "Tier \(.tierLevel) (\(.tierName)): \(.cashbackPercentage)%"'
    
    # Verify values are > 1 (indicating they've been multiplied by 100)
    CASHBACK_VALUES=$(echo "$USER_RESPONSE" | jq -r '.data.tierBenefits.data[]?.cashbackPercentage')
    for value in $CASHBACK_VALUES; do
        if (( $(echo "$value > 1" | bc -l) )); then
            echo -e "${GREEN}✅ Cashback percentage $value appears to be correctly multiplied by 100${NC}"
        else
            echo -e "${YELLOW}⚠️  Cashback percentage $value might not be multiplied by 100${NC}"
        fi
    done
else
    echo -e "${RED}❌ User API request failed${NC}"
    echo "$USER_RESPONSE" | jq '.'
fi

# Test 2: Get user dashboard
echo -e "\n${BLUE}Test 2: User API - Get Dashboard${NC}"
echo "--------------------------------"

DASHBOARD_QUERY='query {
  activityCashbackDashboard {
    success
    message
    data {
      tierBenefit {
        tierLevel
        tierName
        cashbackPercentage
        netFee
      }
      nextTier {
        tierLevel
        tierName
        cashbackPercentage
        netFee
      }
    }
  }
}'

echo "Making request to user dashboard API..."
DASHBOARD_RESPONSE=$(make_graphql_request "$GRAPHQL_ENDPOINT" "$DASHBOARD_QUERY" "$TEST_JWT")

if echo "$DASHBOARD_RESPONSE" | jq -e '.data.activityCashbackDashboard.success' > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Dashboard API request successful${NC}"
    
    # Extract current tier info
    CURRENT_TIER=$(echo "$DASHBOARD_RESPONSE" | jq -r '.data.activityCashbackDashboard.data.tierBenefit')
    if [ "$CURRENT_TIER" != "null" ]; then
        echo -e "\n${BLUE}Current Tier Cashback:${NC}"
        echo "$CURRENT_TIER" | jq -r '"Tier \(.tierLevel) (\(.tierName)): \(.cashbackPercentage)%"'
    fi
    
    # Extract next tier info
    NEXT_TIER=$(echo "$DASHBOARD_RESPONSE" | jq -r '.data.activityCashbackDashboard.data.nextTier')
    if [ "$NEXT_TIER" != "null" ]; then
        echo -e "\n${BLUE}Next Tier Cashback:${NC}"
        echo "$NEXT_TIER" | jq -r '"Tier \(.tierLevel) (\(.tierName)): \(.cashbackPercentage)%"'
    fi
else
    echo -e "${RED}❌ Dashboard API request failed${NC}"
    echo "$DASHBOARD_RESPONSE" | jq '.'
fi

# Test 3: Admin API - Get tier benefits
echo -e "\n${BLUE}Test 3: Admin API - Get Tier Benefits${NC}"
echo "------------------------------------"

ADMIN_TIER_QUERY='query {
  adminGetTierBenefits {
    success
    message
    data {
      id
      tierLevel
      tierName
      minPoints
      cashbackPercentage
      netFee
      isActive
    }
  }
}'

echo "Making request to admin API..."
ADMIN_RESPONSE=$(make_graphql_request "$ADMIN_GRAPHQL_ENDPOINT" "$ADMIN_TIER_QUERY" "$TEST_JWT")

if echo "$ADMIN_RESPONSE" | jq -e '.data.adminGetTierBenefits.success' > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Admin API request successful${NC}"
    
    # Extract and display cashback percentages
    echo -e "\n${BLUE}Admin API Cashback Percentages (should be displayed as percentages):${NC}"
    echo "$ADMIN_RESPONSE" | jq -r '.data.adminGetTierBenefits.data[]? | "Tier \(.tierLevel) (\(.tierName)): \(.cashbackPercentage)%"'
    
    # Verify values are > 1 (indicating they've been multiplied by 100)
    ADMIN_CASHBACK_VALUES=$(echo "$ADMIN_RESPONSE" | jq -r '.data.adminGetTierBenefits.data[]?.cashbackPercentage')
    for value in $ADMIN_CASHBACK_VALUES; do
        if (( $(echo "$value > 1" | bc -l) )); then
            echo -e "${GREEN}✅ Admin cashback percentage $value appears to be correctly multiplied by 100${NC}"
        else
            echo -e "${YELLOW}⚠️  Admin cashback percentage $value might not be multiplied by 100${NC}"
        fi
    done
else
    echo -e "${RED}❌ Admin API request failed${NC}"
    echo "$ADMIN_RESPONSE" | jq '.'
fi

echo -e "\n${GREEN}🎉 Test completed!${NC}"
echo -e "${BLUE}Summary:${NC}"
echo "- User API tier benefits tested"
echo "- User dashboard API tested"
echo "- Admin API tier benefits tested"
echo -e "\n${YELLOW}Note: If you see values like 0.1, 0.15, 0.2 instead of 10, 15, 20, then the multiplication by 100 is not working.${NC}"
echo -e "${YELLOW}Expected values should be like: 10 (for 0.1%), 15 (for 0.15%), 20 (for 0.2%), etc.${NC}"
